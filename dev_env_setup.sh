#!/bin/bash
# Debian 12 Glassmorphic Desktop App Development Environment
# Optimized for creating modern desktop apps with glassmorphic design
set -e

echo "=== Installing Glassmorphic Desktop App Development Environment ==="

# Update and upgrade system
echo "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Essential build tools and dependencies
echo "Installing essential build tools..."
sudo apt install -y build-essential git curl wget unzip pkg-config cmake ninja-build

# Graphics and compositor libraries for glassmorphic effects
echo "Installing graphics libraries for glassmorphic effects..."
sudo apt install -y libcairo2-dev libpango1.0-dev libgdk-pixbuf2.0-dev libatk1.0-dev libgtk-3-dev libgtk-4-dev
sudo apt install -y libadwaita-1-dev gir1.2-adw-1 libwebkit2gtk-4.0-dev libwebkit2gtk-4.1-dev
sudo apt install -y libgl1-mesa-dev libglu1-mesa-dev libglew-dev libglfw3-dev

# Qt6 with QML for advanced UI effects
echo "Installing Qt6 with QML support..."
sudo apt install -y qt6-base-dev qt6-base-dev-tools qt6-declarative-dev qt6-quick3d-dev
sudo apt install -y qml-module-qtquick2 qml-module-qtquick-controls2 qml-module-qtquick-layouts
sudo apt install -y qml-module-qtgraphicaleffects qml-module-qt-labs-platform qml-module-qtquick-window2

# Python with modern GUI frameworks
echo "Installing Python GUI frameworks..."
sudo apt install -y python3 python3-pip python3-venv python3-dev
sudo apt install -y python3-pyqt6 python3-pyside6.qtwidgets python3-pyside6.qtcore python3-pyside6.qtgui python3-pyside6.qtqml
sudo apt install -y python3-gi python3-gi-cairo gir1.2-gtk-3.0 gir1.2-gtk-4.0

# Node.js LTS for Electron and web-based desktop apps
echo "Installing Node.js LTS..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
    sudo apt install -y nodejs
fi

# Modern web frameworks for glassmorphic design
echo "Installing web frameworks and glassmorphic libraries..."
sudo npm install -g @electron/rebuild electron-builder yarn typescript vite
sudo npm install -g tailwindcss @tailwindcss/forms @tailwindcss/typography
sudo npm install -g create-react-app @sveltejs/kit @angular/cli
sudo npm install -g framer-motion three @react-three/fiber @react-three/drei

# Flutter with desktop support
echo "Installing Flutter with desktop support..."
sudo apt install -y libblkid-dev liblzma-dev
if [ ! -d "/opt/flutter" ]; then
    cd /opt
    sudo rm -rf /opt/flutter 2>/dev/null || true
    sudo git clone https://github.com/flutter/flutter.git -b stable --depth 1
    sudo chown -R $USER /opt/flutter
fi
if ! grep -q "/opt/flutter/bin" ~/.bashrc; then
    echo 'export PATH="$PATH:/opt/flutter/bin"' >> ~/.bashrc
fi
export PATH="$PATH:/opt/flutter/bin"
flutter config --enable-linux-desktop

# Rust for Tauri (web tech + native performance)
echo "Installing Rust and Tauri..."
if ! command -v rustc &> /dev/null; then
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
    if ! grep -q "source ~/.cargo/env" ~/.bashrc; then
        echo 'source ~/.cargo/env' >> ~/.bashrc
    fi
fi
source ~/.cargo/env 2>/dev/null || true
if command -v cargo &> /dev/null; then
    cargo install tauri-cli --locked
fi

# .NET 8 SDK for Avalonia and MAUI
echo "Installing .NET 8 SDK..."
if ! command -v dotnet &> /dev/null; then
    wget https://packages.microsoft.com/config/debian/12/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
    sudo dpkg -i packages-microsoft-prod.deb
    sudo apt update
    sudo apt install -y dotnet-sdk-8.0
    rm packages-microsoft-prod.deb
fi
if command -v dotnet &> /dev/null; then
    dotnet workload install maui 2>/dev/null || echo "MAUI workload installation skipped"
    dotnet new install Avalonia.ProjectTemplates 2>/dev/null || echo "Avalonia templates installation skipped"
fi

# Java 17 LTS with JavaFX
echo "Installing Java 17 LTS with JavaFX..."
sudo apt install -y openjdk-17-jdk openjdk-17-jre openjfx maven gradle

# Additional development tools
echo "Installing additional development tools..."
sudo apt install -y glade appstream-util desktop-file-utils meson valac
sudo apt install -y libssl-dev libffi-dev libncurses5-dev libsqlite3-dev libreadline-dev

# Glassmorphic design specific libraries and tools
echo "Installing glassmorphic design libraries..."
sudo apt install -y libskia-dev 2>/dev/null || echo "Skia not available in repos, will need manual install"
sudo apt install -y librsvg2-dev libpixman-1-dev

echo "=== Installation Complete! ==="
echo ""
echo "🎨 GLASSMORPHIC DESKTOP APP DEVELOPMENT ENVIRONMENT READY!"
echo ""
echo "✅ Installed Frameworks & Technologies:"
echo "   🖼️  Qt6 with QML (excellent for glassmorphic effects)"
echo "   🎯  GTK 4 with libadwaita (modern blur effects)"
echo "   🐍  Python: PyQt6, PySide6, GTK bindings"
echo "   🌐  Node.js LTS with glassmorphic web frameworks:"
echo "       - Electron (desktop apps with web tech)"
echo "       - Tailwind CSS (utility-first CSS)"
echo "       - Framer Motion (animations)"
echo "       - Three.js & React Three Fiber (3D effects)"
echo "   🦋  Flutter with desktop support"
echo "   🦀  Rust with Tauri (web tech + native performance)"
echo "   ☕  Java 17 LTS with JavaFX"
echo "   🔷  .NET 8 SDK with Avalonia UI & MAUI"
echo "   📚  Graphics libraries: Cairo, WebKit, OpenGL"
echo ""
echo "🎨 BEST FRAMEWORKS FOR GLASSMORPHIC DESIGN:"
echo "   1. Qt6 + QML - Native blur effects, transparency, animations"
echo "   2. Electron + CSS - backdrop-filter, CSS glassmorphism"
echo "   3. Tauri + Web Tech - Rust performance + web flexibility"
echo "   4. Flutter - Custom painters, blur widgets"
echo "   5. GTK4 + libadwaita - Modern GNOME-style blur effects"
echo ""
echo "🚀 NEXT STEPS:"
echo "   1. Restart terminal: source ~/.bashrc"
echo "   2. Verify installations: flutter doctor, cargo --version, dotnet --version"
echo "   3. Install glassmorphic CSS libraries:"
echo "      npm install glass-ui glassmorphism-css"
echo "   4. For advanced effects, consider:"
echo "      - Lottie animations"
echo "      - CSS backdrop-filter"
echo "      - Qt Quick Effects"
echo "      - Flutter's ImageFilter.blur()"
echo ""
echo "💡 GLASSMORPHIC DESIGN TIPS:"
echo "   - Use semi-transparent backgrounds (rgba)"
echo "   - Apply backdrop blur filters"
echo "   - Add subtle borders and shadows"
echo "   - Use gradient overlays"
echo "   - Implement smooth animations"
echo ""

#!/bin/bash
# Debian 12 Desktop App Dev Environment Setup - Updated with Latest Versions
set -e

# Update and upgrade
sudo apt update && sudo apt upgrade -y

# Essential build tools
sudo apt install -y build-essential git curl wget unzip pkg-config cmake ninja-build

# Latest C/C++ GUI frameworks
# Qt6 (latest) and Qt5 (for compatibility)
sudo apt install -y qt6-base-dev qt6-base-dev-tools qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools
# GTK3 and GTK4 (latest)
sudo apt install -y libgtk-3-dev libgtk-4-dev
# wxWidgets latest
sudo apt install -y libwxgtk3.2-dev

# Python and development tools
sudo apt install -y python3 python3-pip python3-venv python3-dev

#!/bin/bash
# Debian 12 Complete Modern Desktop App Development Environment
set -e

echo "=== Installing Complete Modern Desktop App Development Environment ==="

# Update and upgrade
sudo apt update && sudo apt upgrade -y

# Essential build tools
sudo apt install -y build-essential git curl wget unzip pkg-config cmake ninja-build

# Qt6 (Latest Qt framework) - ALREADY INSTALLED
echo "Qt6 already installed..."

# GTK 4 with libadwaita - ALREADY INSTALLED
echo "GTK 4 with libadwaita already installed..."

# GTK 3 (for compatibility)
echo "Installing GTK 3..."
sudo apt install -y libgtk-3-dev

# wxWidgets - ALREADY INSTALLED
echo "wxWidgets already installed..."

# KDE Frameworks 5 (Qt-based) - ALREADY INSTALLED
echo "KDE Frameworks 5 already installed..."

# Python GUI frameworks
echo "Installing Python GUI frameworks..."
sudo apt install -y python3-pyqt5 python3-pyside2.qtwidgets python3-pyside2.qtcore python3-pyside2.qtgui python3-wxgtk4.0 python3-gi python3-gi-cairo gir1.2-gtk-3.0 gir1.2-gtk-4.0

# Node.js LTS (for Electron, React, Svelte, etc.)
echo "Installing Node.js LTS and web frameworks..."
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt install -y nodejs

# Modern Web Technologies and Frameworks
echo "Installing modern web development tools..."
sudo npm install -g @electron/rebuild electron-builder yarn typescript
sudo npm install -g create-react-app
sudo npm install -g @sveltejs/kit
sudo npm install -g @angular/cli
sudo npm install -g vite
sudo npm install -g tailwindcss

# Material-UI + React (create-react-app includes this capability)
# Svelte with SvelteKit + Tailwind already covered above
# Alpine.js can be added to any project via CDN

# Flutter (with desktop support)
echo "Installing Flutter with desktop support..."
sudo apt install -y libgtk-3-dev libblkid-dev liblzma-dev
cd /opt
if [ ! -d "/opt/flutter" ]; then
    sudo rm -rf /opt/flutter 2>/dev/null || true
    sudo git clone https://github.com/flutter/flutter.git -b stable
    sudo chown -R $USER /opt/flutter
fi
export PATH="$PATH:/opt/flutter/bin"
echo 'export PATH="$PATH:/opt/flutter/bin"' >> ~/.bashrc
flutter config --enable-linux-desktop
echo "Flutter installation completed. Running flutter doctor..."
flutter doctor

# Rust (for Tauri)
echo "Installing Rust and Tauri..."
if ! command -v rustc &> /dev/null; then
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
    echo 'source ~/.cargo/env' >> ~/.bashrc
fi

# Tauri CLI (Rust-based desktop apps with web technologies)
cargo install tauri-cli --locked

# Java 17 LTS and JavaFX
echo "Installing Java 17 LTS..."
sudo apt install -y openjdk-17-jdk openjdk-17-jre openjfx maven gradle

# .NET 8 SDK (for Blazor, .NET MAUI, Avalonia)
echo "Installing .NET 8 SDK..."
if ! command -v dotnet &> /dev/null; then
    wget https://packages.microsoft.com/config/debian/12/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
    sudo dpkg -i packages-microsoft-prod.deb
    sudo apt update
    sudo apt install -y dotnet-sdk-8.0
    rm packages-microsoft-prod.deb
fi

# .NET MAUI and Avalonia templates
echo "Installing .NET templates for MAUI and Avalonia..."
dotnet workload install maui
dotnet new install Avalonia.ProjectTemplates

# Additional desktop development tools
echo "Installing additional development tools..."
sudo apt install -y glade appstream-util desktop-file-utils meson valac

# Liri Shell dependencies (Qt-based modern shell)
echo "Installing Liri Shell dependencies..."
sudo apt install -y qml-module-qtquick2 qml-module-qtquick-controls2 qml-module-qtquick-layouts qml-module-qtgraphicaleffects

# Native development tools
echo "Installing native development essentials..."
sudo apt install -y libssl-dev libffi-dev libncurses5-dev libsqlite3-dev libreadline-dev libtk8.6-dev libgdm-dev libdb4o-cil-dev libpcap-dev

echo "=== Installation Complete! ==="
echo ""
echo "Installed frameworks and technologies:"
echo "✅ Qt6 (latest Qt framework)"
echo "✅ GTK 4 with libadwaita (latest GTK)"
echo "✅ GTK 3 (compatibility)"
echo "✅ wxWidgets"
echo "✅ KDE Frameworks 5"
echo "✅ Python: PyQt5, PySide2, wxPython, GTK bindings"
echo "✅ Node.js LTS with:"
echo "   - Electron (desktop apps with web tech)"
echo "   - React (with Material-UI capability)"
echo "   - Svelte/SvelteKit (with Tailwind CSS)"
echo "   - Angular CLI"
echo "   - Vite (fast build tool)"
echo "   - Tailwind CSS"
echo "✅ Flutter with desktop support"
echo "✅ Rust with Tauri (web tech + native performance)"
echo "✅ Java 17 LTS with JavaFX"
echo "✅ .NET 8 SDK with:"
echo "   - Blazor WebAssembly"
echo "   - .NET MAUI (cross-platform)"
echo "   - Avalonia UI (cross-platform XAML)"
echo "✅ Liri Shell dependencies"
echo ""
echo "Notes:"
echo "- WinUI is Windows-specific (not available on Linux)"
echo "- Neon.js appears to be a typo (possibly meant Neon/Node.js bindings)"
echo "- Alpine.js can be added to any web project via CDN"
echo ""
echo "Please restart your terminal or run 'source ~/.bashrc' to update PATH"
echo "Then run 'flutter doctor' to verify Flutter installation"

# Node.js LTS (for Electron and web technologies)
echo "Installing Node.js LTS..."
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt install -y nodejs

# Web development essentials for Electron
echo "Installing web development tools..."
sudo npm install -g @electron/rebuild electron-builder yarn typescript

# Flutter (with desktop support)
echo "Installing Flutter..."
sudo apt install -y libgtk-3-dev libblkid-dev liblzma-dev
cd /opt
sudo git clone https://github.com/flutter/flutter.git -b stable --depth 1
sudo chown -R $USER /opt/flutter
export PATH="$PATH:/opt/flutter/bin"
echo 'export PATH="$PATH:/opt/flutter/bin"' >> ~/.bashrc
flutter config --enable-linux-desktop
flutter doctor

# Rust (for Tauri and native apps)
echo "Installing Rust..."
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source ~/.cargo/env
echo 'source ~/.cargo/env' >> ~/.bashrc

# Tauri CLI (Rust-based desktop apps)
echo "Installing Tauri..."
cargo install tauri-cli --locked

# Java 17 LTS and JavaFX (Latest LTS)
echo "Installing Java..."
sudo apt install -y openjdk-17-jdk openjdk-17-jre openjfx maven gradle

# .NET 8 SDK (Latest LTS)
echo "Installing .NET 8..."
wget https://packages.microsoft.com/config/debian/12/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt update
sudo apt install -y dotnet-sdk-8.0
rm packages-microsoft-prod.deb

# Additional desktop development tools
echo "Installing additional tools..."
sudo apt install -y glade appstream-util desktop-file-utils meson valac

# Liri Shell dependencies (experimental Qt-based shell)
echo "Installing Liri Shell dependencies..."
sudo apt install -y qml-module-qtquick2 qml-module-qtquick-controls2 qml-module-qtquick-layouts qml-module-qtgraphicaleffects

echo "=== Installation Complete! ==="
echo ""
echo "Installed frameworks:"
echo "- Qt6 (latest Qt framework)"
echo "- GTK 4 with libadwaita (latest GTK)"
echo "- wxWidgets"
echo "- KDE Frameworks 5"
echo "- Python with PyQt6, PySide6, wxPython, GTK bindings"
echo "- Node.js LTS with Electron tools"
echo "- Flutter with desktop support"
echo "- Rust with Tauri"
echo "- Java 17 LTS with JavaFX"
echo "- .NET 8 SDK"
echo "- Liri Shell dependencies"
echo ""
echo "Please restart your terminal or run 'source ~/.bashrc' to update PATH"

---
type: "agent_requested"
description: "This information lists all the environment information. This includes everything from the OS to the frameworks that have been installed."
---
## System Configuration
- **OS**: Debian GNU/Linux 12 (Bookworm)
- **Architecture**: x64 (amd64)
- **IDE**: VS Code Insiders
- **Date Configured**: July 31, 2025

## Installed Desktop App Development Frameworks

### Core Native GUI Frameworks
- **Qt6 (6.4.2)**: Latest Qt framework with QML, QtQuick, multimedia, SVG, and Wayland support
  - Available tools: qmake6, Qt Designer, Qt Creator tools, linguist-qt6
  - Development headers and libraries for C++ and QML development
- **GTK 4 (4.8.3) with libadwaita**: Modern GNOME-style applications
  - Full libadwaita integration for adaptive and modern UI designs
  - GTK 4 development headers and GObject introspection
- **GTK 3**: Legacy compatibility for existing GTK applications
- **wxWidgets (3.2.2)**: Cross-platform native GUI toolkit
  - Available for C++ development with native look and feel
- **KDE Frameworks 5**: Complete KDE/Plasma development stack
  - Extra CMake modules, KConfig, KCoreAddons, KI18n, KWidgetsAddons, KCompletion, KXmlGui, KIO

### Cross-Platform Mobile & Desktop
- **Flutter (3.32.8)**: Google's UI toolkit with Linux desktop support enabled
  - Dart SDK included and configured
  - Linux desktop development fully supported (clang installed)
  - Desktop integration packages available
  - Path: `/opt/flutter/bin` (added to .bashrc)

### Web Technologies & Hybrid Apps
- **Node.js LTS**: Latest LTS version with npm
- **Electron**: Desktop apps with web technologies
  - @electron/rebuild and electron-builder globally installed
- **React.js**: Component-based UI framework (via create-react-app)
  - Material-UI capabilities available
- **Svelte/SvelteKit**: Modern reactive framework with SSR support
- **Angular CLI**: Full-featured TypeScript framework
- **Vite**: Fast build tool for modern web development
- **Tailwind CSS**: Utility-first CSS framework
- **TypeScript**: Typed JavaScript with latest language features
- **Yarn**: Alternative package manager to npm

### Systems Programming & Modern Native
- **Rust**: Complete Rust toolchain with cargo
  - **Tauri CLI**: Web frontend + Rust backend desktop apps
  - Rust-analyzer for VS Code integration
- **C/C++**: Multiple compiler toolchains
  - **Clang/LLVM 14**: Modern C/C++ compiler
  - **GCC**: GNU Compiler Collection
  - Full development headers and build tools

### Enterprise & Cross-Platform Frameworks
- **Java Ecosystem**:
  - **OpenJDK 17 LTS**: Long-term support Java development kit
  - **JavaFX**: Rich client platform for desktop applications
  - **Maven**: Build automation and dependency management
  - **Gradle**: Advanced build tool with Groovy/Kotlin DSL
- **.NET 8 SDK**: Latest LTS version with extensive capabilities
  - **Blazor WebAssembly**: Web applications with C#
  - **.NET MAUI**: Cross-platform native apps (templates installed)
  - **Avalonia UI**: Cross-platform XAML-based apps (templates installed)

### Python GUI Development
- **Python 3.11** with comprehensive GUI bindings:
  - **PyQt5**: Qt5 bindings for Python
  - **PySide2**: Official Qt bindings for Python (multiple modules available)
  - **wxPython**: wxWidgets bindings for Python
  - **PyGObject**: GTK 3/4 bindings for Python with Cairo support
  - **GObject Introspection**: GTK 3.0 and GTK 4.0 modules

## Development Tools & Build Systems
- **Git**: Version control system
- **CMake**: Cross-platform build system generator
- **Ninja**: Fast build system
- **Meson**: Modern build system
- **pkg-config**: Library compilation and linking helper
- **Glade**: GTK UI designer
- **Qt Designer**: Qt UI designer (included with Qt6)
- **Valac**: Vala compiler for GTK development

## Package Managers Available
- **apt**: Debian package manager
- **npm**: Node.js package manager
- **yarn**: Alternative Node.js package manager
- **pip**: Python package installer
- **cargo**: Rust package manager
- **dotnet**: .NET package manager (NuGet)
- **maven**: Java dependency management
- **gradle**: Java/Kotlin build tool

## VS Code Extensions Installed
- **Flutter & Dart**: Complete Flutter development support
- **Python**: Python development with IntelliSense
- **Rust Analyzer**: Advanced Rust language support
- **C# Dev Kit**: .NET and C# development
- **TypeScript**: JavaScript and TypeScript language features

## Development Capabilities

### Native Desktop Applications
- **Linux-native apps**: Using Qt6, GTK4, or wxWidgets
- **KDE/Plasma apps**: Using KDE Frameworks 5
- **GNOME apps**: Using GTK4 with libadwaita
- **System applications**: Using Rust or C/C++

### Cross-Platform Desktop Applications
- **Flutter**: Single codebase for mobile and desktop
- **Electron**: Web technologies in native wrapper
- **Tauri**: Rust backend with web frontend (smaller, faster than Electron)
- **.NET MAUI**: Native performance with shared C# codebase
- **Avalonia**: XAML-based cross-platform apps
- **Qt6**: Write once, deploy anywhere with native performance

### Web Applications
- **React**: Component-based SPAs with Material-UI
- **Svelte/SvelteKit**: Reactive framework with server-side rendering
- **Angular**: Enterprise-grade TypeScript framework
- **Blazor**: C# web applications (Server and WebAssembly)

### Mobile Applications
- **Flutter**: Native mobile apps for iOS and Android from single codebase

## Code Assistance Guidelines

### When suggesting frameworks:
1. **For beginners**: Recommend Python with PyQt5/PySide2 or Flutter
2. **For web developers**: Suggest Electron or Tauri
3. **For performance**: Recommend Qt6 with C++, Rust with Tauri, or native C/C++
4. **For cross-platform**: Prioritize Flutter, .NET MAUI, or Qt6
5. **For Linux-specific**: Suggest GTK4 with libadwaita or KDE Frameworks

### Build system preferences:
- **CMake** for C/C++ projects
- **Meson** for GTK/GNOME projects
- **Cargo** for Rust projects
- **npm/yarn** for Node.js/Electron projects
- **dotnet** for .NET projects
- **flutter** commands for Flutter projects

### Environment considerations:
- All frameworks support Wayland and X11
- Desktop integration packages are available
- Modern theming support through libadwaita (GTK) and Qt6 styling
- Accessibility features supported across all frameworks

## Path Configuration
- Flutter: `/opt/flutter/bin` (in PATH)
- Rust: `~/.cargo/env` sourced in .bashrc
- All other tools available in standard system PATH

## Project Templates Available
- Flutter: `flutter create --platforms=linux,android,ios,web <app_name>`
- .NET MAUI: `dotnet new maui -n <app_name>`
- Avalonia: `dotnet new avalonia.app -n <app_name>`
- React: `npx create-react-app <app_name>`
- Svelte: `npm create svelte@latest <app_name>`
- Angular: `ng new <app_name>`
- Tauri: `cargo install create-tauri-app && cargo create-tauri-app`

When providing code examples or project setup advice, reference the specific versions and capabilities of these installed frameworks. Prioritize modern, maintained frameworks and suggest appropriate alternatives based on project requirements, developer experience, and performance needs.

Please understand that these are not the only frameworks available. If you find that you need a framework that isnt already installed, please install it.